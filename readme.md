# Warehouse Unloading Simulation

A discrete event simulation system for warehouse unloading operations using SimPy.

## Overview

This project simulates warehouse unloading procedures, focusing on the movement of fresh and ambient pallets from trucks to storage areas. The simulation accounts for real-world constraints including:

- Truck arrivals at docking ports
- Forklift operations and movements
- Storage area capacities
- Passageway traffic limitations
- Lift operations for multi-level storage

## Features

- Discrete event simulation using SimPy
- Data-driven configuration using Excel input files
- Performance metrics reporting

## Project Structure

```
warehouse_simulation/
├── src/                # Source code
│   ├── models/         # Core simulation models
│   ├── utils/          # Utilities
│   └── simulation.py   # Main simulation runner
├── config/             # Configuration files
├── data/               # Input data files
├── output/             # Simulation results
└── docs/               # Documentation
```

## Requirements

- Python
- SimPy
- Pandas
- NumPy
- Matplotlib

## Setup

1. Clone this repository
2. Install dependencies: `pip install -r requirements.txt`
3. Place input data files in the `data/` directory

## Usage

Run the simulation:

```bash
python main.py
```

## Input Data

The simulation requires the following input data:
- Pallet information (arrival time, cargo type, store ID)
- Simulation parameters (resource limits, processing times)
- Lift specifications (when applicable)
