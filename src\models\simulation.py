"""
Simulation module for warehouse simulation.
Contains the main simulation class and related functions.

created on 11.04.2025 by <PERSON><PERSON><PERSON>
updated on 16.07.2025
"""

import simpy
from typing import List
import logging

from src.models.entities import Truck, Pallet
from src.models.model_enums import CargoType, EntityLocation, Port

# setup logging
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
formatter = logging.Formatter('%(levelname)s - %(name)s - %(message)s')
filehandler = logging.FileHandler('./src/logs/simulation.log')
filehandler.setFormatter(formatter)
logger.addHandler(filehandler)

class Simulation:
    """Simulation class for inbound and outbound operations in the warehouse"""

    def __init__(self, env, config, inbound_trucks: List[Truck], outbound_pallets: List[Pallet]):
        self.env = env
        self.config = config
        self.inbound_trucks = inbound_trucks
        self.outbound_pallets = outbound_pallets

        # Resources
        self.dc_dock_pool = simpy.Resource(env, capacity=self.config.DC_DOCKS)
        self.dsd_dock_pool = simpy.Resource(env, capacity=self.config.DSD_DOCKS)
        self.passageway_forklift_pool = simpy.Resource(env, capacity=self.config.PASSAGEWAY_FORKLIFT_TRHROUGHPUT_LIMIT)
        self.lift_forklifts_pool = simpy.Resource(env, capacity=self.config.LIFT_AREA_FORKLIFT_LIMIT)
        self.fresh_storage_forklift_pool = simpy.Resource(env, capacity=self.config.STORAGE_AREA_FRESH_FORKLIFT_LIMIT)
        self.ambient_storage_forklift_pool = simpy.Resource(env, capacity=self.config.STORAGE_AREA_AMBIENT_FORKLIFT_LIMIT)
        self.lift_pool = simpy.Resource(env, capacity=self.config.LIFT_COUNT)

        self.inbound_dc_forklift_pool = simpy.Resource(env, capacity=self.config.DC_FORKLIFT_OPERATION_LIMIT)
        self.inbound_dsd_forklift_pool = simpy.Resource(env, capacity=self.config.DSD_FORKLIFT_OPERATION_LIMIT)
        self.inbound_lift_pool = simpy.Resource(env, capacity=self.config.LIFT_USAGE_LIMIT_INBOUND)

        self.outbound_fresh_storage_forklift_pool = simpy.Resource(env, capacity=self.config.STORAGE_AREA_FRESH_FORKLIFT_LIMIT_OUTBOUND)
        self.outbound_ambient_storage_forklift_pool = simpy.Resource(env, capacity=self.config.STORAGE_AREA_AMBIENT_FORKLIFT_LIMIT_OUTBOUND)
        self.outbound_lift_forklift_pool = simpy.Resource(env, capacity=self.config.LIFT_AREA_FORKLIFT_LIMIT_OUTBOUND)
        self.outbound_lift_pool = simpy.Resource(env, capacity=self.config.LIFT_USAGE_LIMIT_OUTBOUND)
        self.outbound_passageway_forklift_pool = simpy.Resource(env, capacity=self.config.PASSAGEWAY_FORKLIFT_TRHOUGHPUT_LIMIT_OUTBOUND)
        self.outbound_dc_forklift_pool = simpy.Resource(env, capacity=self.config.DC_FORKLIFT_OUTBOUND_OPERATION_LIMIT)
        self.outbound_dsd_forklift_pool = simpy.Resource(env, capacity=self.config.DSD_FORKLIFT_OUTBOUND_OPERATION_LIMIT)

        # Stores
        self.inbound_truck_pool = simpy.Store(env, capacity=len(self.inbound_trucks))
        self.fresh_storage = simpy.Store(env, capacity=config.STORAGE_AREA_FRESH_PALLET_CAPACITY)
        self.ambient_storage = simpy.Store(env, capacity=config.STORAGE_AREA_AMBIENT_PALLET_CAPACITY)
        self.outbound_pallet_pool = simpy.Store(env, capacity=len(self.outbound_pallets))

        # add inbound trucks to the pool
        for inbound_truck in self.inbound_trucks:
            self.inbound_truck_pool.put(inbound_truck)

        # add outbound pallets to the pool
        for outbound_pallet in self.outbound_pallets:
            self.outbound_pallet_pool.put(outbound_pallet)

        # tracking
        self.inbound_pallets_track = []
        self.outbound_pallets_track = []

        # run the inbound simulation
        for inbound_truck in self.inbound_trucks:
            self.env.process(self.inbound_simulation())

        # run the outbound simulation
        for outbound_pallet in self.outbound_pallets:
            self.env.process(self.outbound_simulation())

    def inbound_simulation(self):
        """Simulate the inbound operations in the warehouse"""
        # wait till the simulation start time
        yield self.env.timeout(self.config.SIMULATION_START_TIME.hour * 3600 + self.config.SIMULATION_START_TIME.minute * 60 + self.config.SIMULATION_START_TIME.second)

        # simulate truck arrival
        inbound_truck = yield self.inbound_truck_pool.get()

        # wait until the truck's arrival time
        if inbound_truck.arrival_time > self.env.now:
            yield self.env.timeout(inbound_truck.arrival_time - self.env.now)

        # check if there is enough storage capacity before unloading
        available_fresh_storage = self.fresh_storage.capacity - len(self.fresh_storage.items) - self._get_outbound_fresh_pallet_count()
        available_ambient_storage = self.ambient_storage.capacity - len(self.ambient_storage.items) - self._get_outbound_ambient_pallet_count()

        # check if there is enough storage capacity before unloading
        if inbound_truck.ambient_pallets_count <= available_ambient_storage and inbound_truck.fresh_pallets_count <= available_fresh_storage:
            logger.info(f"Truck {inbound_truck.entity_id} arrived at {inbound_truck.arrival_time} s")

            # store the pallets in truck for simulation
            inbound_pallets = simpy.Store(self.env, capacity=len(inbound_truck.pallets))

            for pallet in inbound_truck.pallets:
                inbound_pallets.put(pallet)

            # select the unloading dock
            unloading_dock_pool = self._get_unloading_dock_pool(inbound_truck)

            # request a dock from the selected dock
            unloading_dock = unloading_dock_pool.request()
            yield unloading_dock

            inbound_truck.dock_time = self.env.now
            logger.info(f"Truck {inbound_truck.entity_id} docked at {self.env.now} s with {inbound_truck.capacity} pallets")

            # setup unloading parameters
            inbound_dock_forklift_pool = self._get_inbound_dock_forklift_pool(inbound_truck)

            # unload the pallets
            for i, pallet in enumerate(inbound_truck.pallets):
                if i == len(inbound_truck.pallets) - 1:
                    is_last_pallet = True
                else:
                    is_last_pallet = False

                self.env.process(self._process_inbound_pallet(inbound_pallets, inbound_dock_forklift_pool, unloading_dock_pool, unloading_dock, is_last_pallet))

        else:
            logger.warning(f"Truck {inbound_truck.entity_id} arrived at {inbound_truck.arrival_time} s but there is not enough storage capacity")

            # put truck back to the store
            self.inbound_truck_pool.put(inbound_truck)

    def _process_inbound_pallet(self, inbound_pallets: simpy.Store, inbound_dock_forklift_pool: simpy.Resource, unloading_dock_pool: simpy.Resource, unloading_dock, is_last_pallet: bool):
        """Process the pallet in the inbound simulation."""
        # process a pallet
        pallet = yield inbound_pallets.get()

        # track pallets entering the system
        self.inbound_pallets_track.append(pallet)

        # request a forklift from the selected dock
        inbound_dock_forklift = inbound_dock_forklift_pool.request()
        yield inbound_dock_forklift
        pallet.dock_forklift_on_time = self.env.now

        # unload a pallet
        pallet.inbound_unload_start_time_actual = self.env.now
        yield self.env.timeout(pallet.dock_unload_duration)
        pallet.inbound_unload_finish_time_actual = self.env.now
        pallet.location = EntityLocation.UNLOADING_AREA
        logger.info(f"Pallet {pallet.entity_id} unloaded at {self.env.now} s")

        # check a pallet
        yield self.env.timeout(pallet.check_pallet_duration)
        pallet.inbound_register_time_finish_actual = self.env.now
        logger.info(f"Pallet {pallet.entity_id} checked at {self.env.now} s")

        # request a forklift from the passageway
        passageway_forklift = self.passageway_forklift_pool.request()
        yield passageway_forklift
        pallet.passageway_forklift_on_time = self.env.now

        # pallet left the loading area
        # if there is waiting time, it should be added below this line
        pallet.inbound_left_loading_area_time_actual = self.env.now
        pallet.location = EntityLocation.PASSAGEWAY

        # release the dock forklift
        inbound_dock_forklift_pool.release(inbound_dock_forklift)
        pallet.dock_forklift_off_time = self.env.now

        if pallet.is_lift_required:
            # move the pallet to the lift area
            lift_forklift = self.lift_forklifts_pool.request()
            yield lift_forklift
            pallet.lift_forklift_on_time = self.env.now

            # move pallet to lift area
            yield self.env.timeout(pallet.travel_time_dock_to_lift)
            pallet.inbound_lift_area_arrival_time_actual = self.env.now
            pallet.location = EntityLocation.LIFT_AREA

            # release the passageway forklift
            self.passageway_forklift_pool.release(passageway_forklift)
            pallet.passageway_forklift_off_time = self.env.now

            # load the pallet to the lift
            inbound_lift = self.inbound_lift_pool.request()
            yield inbound_lift
            lift = self.lift_pool.request()
            yield lift
            pallet.lift_on_time = self.env.now

            yield self.env.timeout(self.config.LIFT_FORKLIFT_LOAD_DURATION)
            pallet.inbound_lift_loading_finish_time_actual = self.env.now
            logger.info(f"Pallet {pallet.entity_id} loaded to lift at {self.env.now} s")
            pallet.location = EntityLocation.LIFT

            # release the forklifts
            self.lift_forklifts_pool.release(lift_forklift)
            pallet.lift_forklift_off_time = self.env.now

            # move lift between floors
            yield self.env.timeout(self.config.SINGLE_TRIP_LIFT_DURATION)

            # unload the pallet from the lift
            storage_forklift_pool = self._get_storage_forklift_pool(pallet)
            storage_forklift = storage_forklift_pool.request()
            yield storage_forklift
            pallet.storage_forklift_on_time = self.env.now

            # unload the pallet from the lift
            yield self.env.timeout(self.config.LIFT_FORKLIFT_UNLOAD_DURATION)
            pallet.inbound_lift_unloading_finish_time_actual = self.env.now
            pallet.location = EntityLocation.STORAGE_AREA

            # release the lift
            self.lift_pool.release(lift)
            self.inbound_lift_pool.release(inbound_lift)
            pallet.lift_off_time = self.env.now

            # move pallet from lift to storage
            yield self.env.timeout(pallet.travel_time_lift_to_storage)
            pallet.inbound_storage_area_arrival_time_actual = self.env.now

            # store the pallet
            yield self.env.timeout(pallet.storage_unload_duration)
            self._store_pallet(pallet)
            pallet.inbound_pallet_stored_time = self.env.now
            logger.info(f"Pallet {pallet.entity_id} stored at storage at {self.env.now} s")

            # release the forklifts
            storage_forklift_pool.release(storage_forklift)
            pallet.storage_forklift_off_time = self.env.now

        else:
            # store pallet that do not require lift
            storage_forklift_pool = self._get_storage_forklift_pool(pallet)
            storage_forklift = storage_forklift_pool.request()
            yield storage_forklift
            pallet.storage_forklift_on_time = self.env.now

            # move pallet to storage
            yield self.env.timeout(pallet.travel_time_dock_to_storage)
            pallet.inbound_storage_area_arrival_time_actual = self.env.now
            pallet.location = EntityLocation.STORAGE_AREA

            # release the passageway forklift
            self.passageway_forklift_pool.release(passageway_forklift)
            pallet.passageway_forklift_off_time = self.env.now

            # store the pallet
            yield self.env.timeout(pallet.storage_unload_duration)
            pallet.inbound_pallet_stored_time = self.env.now
            self._store_pallet(pallet)
            logger.info(f"Pallet {pallet.entity_id} stored at storage at {self.env.now} s")

            # release the forklifts
            storage_forklift_pool.release(storage_forklift)
            pallet.storage_forklift_off_time = self.env.now

        # release the unloading dock
        if is_last_pallet:
            unloading_dock_pool.release(unloading_dock)
            logger.info(f"Truck {pallet.truck_id} undocked at {self.env.now} s")

    def outbound_simulation(self):
        """Simulate the outbound operation of a pallet in the warehouse"""
        # wait till the simulation start time
        yield self.env.timeout(self.config.SIMULATION_START_TIME.hour * 3600 + self.config.SIMULATION_START_TIME.minute * 60 + self.config.SIMULATION_START_TIME.second)

        # select a pallet from the outbound pallet pool
        pallet = yield self.outbound_pallet_pool.get()

        # track outbound pallets
        self.outbound_pallets_track.append(pallet)

        # wait till the pallet departure time
        if pallet.arrival_time > self.env.now:
            yield self.env.timeout(pallet.arrival_time - self.env.now)

        # request an outbound forklift from the storage area
        outbound_storage_forklift_pool = self._get_outbound_storage_forklift_pool(pallet)
        outbound_storage_forklift = outbound_storage_forklift_pool.request()
        yield outbound_storage_forklift
        storage_forklift_pool = self._get_storage_forklift_pool(pallet)
        storage_forklift = storage_forklift_pool.request()
        yield storage_forklift
        pallet.storage_forklift_on_time = self.env.now

        # load the pallet to forklift
        pallet.outbound_storage_load_time_actual = self.env.now
        yield self.env.timeout(pallet.storage_load_duration)
        pallet.outbound_storage_load_finish_time_actual = self.env.now
        logger.info(f"Pallet {pallet.entity_id} loaded to forklift at {self.env.now} s")

        if pallet.is_lift_required:
            # move the pallet to lift area
            pallet.outbound_left_storage_area_time_actual = self.env.now
            yield self.env.timeout(pallet.travel_time_lift_to_storage)
            pallet.outbound_lift_area_arrival_time_actual = self.env.now
            logger.info(f"Pallet {pallet.entity_id} left storage area at {self.env.now} s")

            # move the pallet to lift
            outbound_lift = self.outbound_lift_pool.request()
            yield outbound_lift
            lift = self.lift_pool.request()
            yield lift
            pallet.lift_on_time = self.env.now

            yield self.env.timeout(self.config.LIFT_FORKLIFT_LOAD_DURATION)
            pallet.outbound_lift_loading_finish_time_actual = self.env.now
            logger.info(f"Pallet {pallet.entity_id} loaded to lift at {self.env.now} s")
            pallet.location = EntityLocation.LIFT

            # release the storage area forklifts
            storage_forklift_pool.release(storage_forklift)
            outbound_storage_forklift_pool.release(outbound_storage_forklift)
            pallet.storage_forklift_off_time = self.env.now

            # move lift between floors
            yield self.env.timeout(self.config.SINGLE_TRIP_LIFT_DURATION)

            # unload the pallet from lift
            outbound_lift_forklift = self.outbound_lift_forklift_pool.request()
            yield outbound_lift_forklift
            lift_forklift = self.lift_forklifts_pool.request()
            yield lift_forklift
            pallet.lift_forklift_on_time = self.env.now

            yield self.env.timeout(self.config.LIFT_FORKLIFT_UNLOAD_DURATION)
            pallet.outbound_lift_unloading_finish_time_actual = self.env.now
            logger.info(f"Pallet {pallet.entity_id} unloaded from lift at {self.env.now} s")
            pallet.location = EntityLocation.LIFT_AREA

            # release the lift
            self.lift_pool.release(lift)
            self.outbound_lift_pool.release(outbound_lift)
            pallet.lift_off_time = self.env.now

            # move the pallet from lift to unloading area
            outbound_passageway_forklift = self.outbound_passageway_forklift_pool.request()
            yield outbound_passageway_forklift
            passageway_forklift = self.passageway_forklift_pool.request()
            yield passageway_forklift
            pallet.passageway_forklift_on_time = self.env.now

            # release the lift area forklifts
            self.lift_forklifts_pool.release(lift_forklift)
            self.outbound_lift_forklift_pool.release(outbound_lift_forklift)
            pallet.lift_forklift_off_time = self.env.now

            # move pallet to passageway
            pallet.outbound_lift_area_departure_time_actual = self.env.now
            pallet.location = EntityLocation.PASSAGEWAY

            # move pallet to unloading area
            outbound_dock_forklift_pool = self._get_outbound_dock_forklift_pool(pallet)
            outbound_dock_forklift = outbound_dock_forklift_pool.request()
            yield outbound_dock_forklift
            pallet.dock_forklift_on_time = self.env.now

            yield self.env.timeout(pallet.travel_time_dock_to_lift)
            pallet.outbound_loading_area_arrival_time_actual = self.env.now
            logger.info(f"Pallet {pallet.entity_id} arrived at unloading area at {self.env.now} s")
            pallet.location = EntityLocation.UNLOADING_AREA

            # release the passageway forklifts
            self.passageway_forklift_pool.release(passageway_forklift)
            self.outbound_passageway_forklift_pool.release(outbound_passageway_forklift)
            pallet.passageway_forklift_off_time = self.env.now

            # load the pallet to the truck
            yield self.env.timeout(pallet.dock_load_duration)
            pallet.outbound_truck_load_time_actual = self.env.now
            logger.info(f"Pallet {pallet.entity_id} loaded to truck at {self.env.now} s")
            pallet.location = EntityLocation.TRUCK

            # release the forklifts
            outbound_dock_forklift_pool.release(outbound_dock_forklift)
            pallet.dock_forklift_off_time = self.env.now

        else:
            # move pallet to unloading area
            outbound_passageway_forklift = self.outbound_passageway_forklift_pool.request()
            yield outbound_passageway_forklift
            passageway_forklift = self.passageway_forklift_pool.request()
            yield passageway_forklift
            pallet.passageway_forklift_on_time = self.env.now

            # release the forklifts
            storage_forklift_pool.release(storage_forklift)
            outbound_storage_forklift_pool.release(outbound_storage_forklift)
            pallet.storage_forklift_off_time = self.env.now

            # move pallet to passageway
            pallet.outbound_left_storage_area_time_actual = self.env.now
            pallet.location = EntityLocation.PASSAGEWAY

            # move pallet to unloading area
            outbound_dock_forklift_pool = self._get_outbound_dock_forklift_pool(pallet)
            outbound_dock_forklift = outbound_dock_forklift_pool.request()
            yield outbound_dock_forklift
            pallet.dock_forklift_on_time = self.env.now

            yield self.env.timeout(pallet.travel_time_dock_to_storage)
            pallet.outbound_loading_area_arrival_time_actual = self.env.now
            logger.info(f"Pallet {pallet.entity_id} arrived at unloading area at {self.env.now} s")
            pallet.location = EntityLocation.UNLOADING_AREA

            # release the forklifts
            self.passageway_forklift_pool.release(passageway_forklift)
            self.outbound_passageway_forklift_pool.release(outbound_passageway_forklift)
            pallet.passageway_forklift_off_time = self.env.now

            # load the pallet to the truck
            yield self.env.timeout(pallet.dock_load_duration)
            pallet.outbound_truck_load_time_actual = self.env.now
            logger.info(f"Pallet {pallet.entity_id} loaded to truck at {self.env.now} s")
            pallet.location = EntityLocation.TRUCK

            # release the forklifts
            outbound_dock_forklift_pool.release(outbound_dock_forklift)
            pallet.dock_forklift_off_time = self.env.now

    def _get_unloading_dock_pool(self, truck: Truck) -> simpy.Resource:
        """Get the appropriate unloading dock pool for the truck."""
        if truck.unloading_port == Port.DSD:
            return self.dsd_dock_pool
        else:
            return self.dc_dock_pool

    def _get_storage_forklift_pool(self, pallet: Pallet) -> simpy.Resource:
        """Get the appropriate storage area forklift pool for the pallet."""
        if pallet.cargo_type == CargoType.FRESH:
            return self.fresh_storage_forklift_pool
        else:
            return self.ambient_storage_forklift_pool

    def _store_pallet(self, pallet: Pallet):
        """Store the pallet in the appropriate storage area."""
        if pallet.cargo_type == CargoType.FRESH:
            if len(self.fresh_storage.items) == self.fresh_storage.capacity:
                logger.warning(f"Pallet {pallet.entity_id} could not be stored at storage at {self.env.now} s")

            self.fresh_storage.put(pallet)
        else:
            if len(self.ambient_storage.items) == self.ambient_storage.capacity:
               logger.warning(f"Pallet {pallet.entity_id} could not be stored at storage at {self.env.now} s")

            self.ambient_storage.put(pallet)

    def _get_inbound_dock_forklift_pool(self, pallet: Pallet) -> simpy.Resource:
        """Get the appropriate unload forklift pool for the pallet."""
        if pallet.unloading_port == Port.DSD:
            return self.inbound_dsd_forklift_pool
        else:
            return self.inbound_dc_forklift_pool

    def _get_outbound_dock_forklift_pool(self, pallet: Pallet) -> simpy.Resource:
        """Get the appropriate outbound unload forklift pool for the pallet."""
        if pallet.unloading_port == Port.DSD:
            return self.outbound_dsd_forklift_pool
        else:
            return self.outbound_dc_forklift_pool

    def _get_outbound_storage_forklift_pool(self, pallet: Pallet) -> simpy.Resource:
        """Get the appropriate outbound storage area forklift for the pallet."""
        if pallet.cargo_type == CargoType.FRESH:
            return self.outbound_fresh_storage_forklift_pool
        else:
            return self.outbound_ambient_storage_forklift_pool

    def _get_outbound_fresh_pallet_count(self) -> int:
        """Get the number of fresh pallets in the outbound pallet pool."""
        return sum(1 for pallet in self.outbound_pallet_pool.items if pallet.cargo_type == CargoType.FRESH)

    def _get_outbound_ambient_pallet_count(self) -> int:
        """Get the number of ambient pallets in the outbound pallet pool."""
        return sum(1 for pallet in self.outbound_pallet_pool.items if pallet.cargo_type == CargoType.AMBIENT)