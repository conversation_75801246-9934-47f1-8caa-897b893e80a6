"""
Enums to use in the warehouse simulation.

created on 12.04.2025 by <PERSON><PERSON><PERSON>
"""

import enum

class CargoType(enum.Enum):
    """Enum representing types of cargo."""
    FRESH = "fresh"
    AMBIENT = "ambient"

class EntityLocation(enum.Enum):
    """Enum representing locations of entities."""
    TRUCK = "truck"
    UNLOADING_AREA = "unloading_area"
    PASSAGEWAY = "passageway"
    LIFT_AREA = "lift_area"
    LIFT = "lift"
    STORAGE_AREA = "storage_area"

class Port(enum.Enum):
    """Enum representing unloading ports."""
    DC = "DC"
    DSD = "DSD"
