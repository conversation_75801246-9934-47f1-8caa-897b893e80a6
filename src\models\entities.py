"""
Entities module for warehouse simulation.
Contains all entity classes representing physical objects in the warehouse.

created on 06.04.2025 by <PERSON><PERSON><PERSON>
updated on 24.05.2025
"""
import simpy
from typing import Optional, List, Union

from src.models.model_enums import CargoType, EntityLocation, Port
from src.utils.config_parser import ConfigParser

class Entity:
    """Base class for all physical entities in the simulation."""
    
    def __init__(self, env: simpy.Environment, config: ConfigParser, entity_id: Union[str, int]):
        self.env = env
        self.config = config
        self.entity_id = entity_id
        self.created_at = env.now

class Truck(Entity):
    """Represents a truck arriving at the warehouse with pallets."""
    
    def __init__(self, env: simpy.Environment, config: ConfigParser, entity_id: Union[str, int], store_id: int, unloading_port: Port, arrival_time: int, pallets: List['Pallet']=[]):
        super().__init__(env, config, entity_id)
        self.store_id = store_id
        self.unloading_port = unloading_port
        self.arrival_time = arrival_time
        self.pallets = pallets

        # tracking timestamps through the system
        self.dock_time = None
        self.departure_time = None

    @property
    def capacity(self):
        """Returns the number of pallets loaded on the truck"""
        return len(self.pallets)
    
    @property
    def ambient_pallets_count(self):
        """Returns the number of ambient pallets loaded on the truck"""
        return sum(1 for pallet in self.pallets if pallet.cargo_type == CargoType.AMBIENT)
    
    @property
    def fresh_pallets_count(self):
        """Returns the number of fresh pallets loaded on the truck"""
        return sum(1 for pallet in self.pallets if pallet.cargo_type == CargoType.FRESH)

class Pallet(Entity):
    """Represents a pallet of goods to be moved through the warehouse"""

    def __init__(self, env: simpy.Environment, config: ConfigParser, entity_id: Union[str, int], store_id: int, unloading_port: Port, cargo_type: CargoType, location: EntityLocation, truck_id: int, arrival_time: int):

        super().__init__(env, config, entity_id)
        self.store_id = store_id
        self.unloading_port = unloading_port
        self.cargo_type = cargo_type
        self.location = location
        self.truck_id = truck_id
        self.arrival_time = arrival_time

        # Tracking timestamps through the system
        # the following inbound parameters were defined based on
        # the sample_output_v2 file received from the client on 2025.03.19
        self.inbound_unload_start_time_planed: Optional[float] = None
        self.inbound_unload_start_time_actual: Optional[float] = None
        self.inbound_unload_finish_time_planed: Optional[float] = None
        self.inbound_unload_finish_time_actual: Optional[float] = None
        self.inbound_register_time_finish_planned: Optional[float] = None
        self.inbound_register_time_finish_actual: Optional[float] = None
        self.inbound_left_loading_area_time_planned: Optional[float] = None
        self.inbound_left_loading_area_time_actual: Optional[float] = None
        self.inbound_lift_area_arrival_time_planned: Optional[float] = None
        self.inbound_lift_area_arrival_time_actual: Optional[float] = None
        self.inbound_lift_loading_finish_time_planned: Optional[float] = None
        self.inbound_lift_loading_finish_time_actual: Optional[float] = None
        self.inbound_lift_unloading_finish_time_planned: Optional[float] = None
        self.inbound_lift_unloading_finish_time_actual: Optional[float] = None
        self.inbound_storage_area_arrival_time_planned: Optional[float] = None
        self.inbound_storage_area_arrival_time_actual: Optional[float] = None
        self.inbound_pallet_stored_time: Optional[float] = None

        # the following outbound parameters were defined based on
        # the sample_outbound_v1 file received from the client on 2025.03.20
        self.outbound_storage_load_time_planned: Optional[float] = None
        self.outbound_storage_load_time_actual: Optional[float] = None
        self.outbound_storage_load_finish_time_planned: Optional[float] = None
        self.outbound_storage_load_finish_time_actual: Optional[float] = None
        self.outbound_left_storage_area_time_planned: Optional[float] = None
        self.outbound_left_storage_area_time_actual: Optional[float] = None
        self.outbound_lift_area_arrival_time_planned: Optional[float] = None
        self.outbound_lift_area_arrival_time_actual: Optional[float] = None
        self.outbound_lift_loading_finish_time_planned: Optional[float] = None
        self.outbound_lift_loading_finish_time_actual: Optional[float] = None
        self.outbound_lift_unloading_finish_time_planned: Optional[float] = None
        self.outbound_lift_unloading_finish_time_actual: Optional[float] = None
        self.outbound_lift_area_departure_time_planned: Optional[float] = None
        self.outbound_lift_area_departure_time_actual: Optional[float] = None
        self.outbound_loading_area_arrival_time_planned: Optional[float] = None
        self.outbound_loading_area_arrival_time_actual: Optional[float] = None
        self.outbound_truck_load_time_planned: Optional[float] = None
        self.outbound_truck_load_time_actual: Optional[float] = None

        # track forklift utilization
        self.dock_forklift_on_time: Optional[float] = None
        self.dock_forklift_off_time: Optional[float] = None
        self.passageway_forklift_on_time: Optional[float] = None
        self.passageway_forklift_off_time: Optional[float] = None
        self.lift_forklift_on_time: Optional[float] = None
        self.lift_forklift_off_time: Optional[float] = None
        self.storage_forklift_on_time: Optional[float] = None
        self.storage_forklift_off_time: Optional[float] = None

        # track lift utilization
        self.lift_on_time: Optional[float] = None
        self.lift_off_time: Optional[float] = None

    @property
    def dock_unload_duration(self):
        """Returns the unload duration for the pallet"""
        if self.unloading_port == Port.DSD:
            return self.config.DSD_FORKLIFT_UNLOAD_DURATION
        else:
            return self.config.DC_FORKLIFT_UNLOAD_DURATION
        
    @property
    def check_pallet_duration(self):
        """Returns the pallet check duration for the pallet"""
        if self.unloading_port == Port.DSD:
            return self.config.DSD_PALLET_COUNT_CHECK_DURATION
        else:
            return 0
    
    @property
    def travel_time_dock_to_lift(self):
        """Returns the travel time from the unloading port to the lift area"""
        if self.unloading_port == Port.DSD:
            return self.config.TRAVEL_TIME_DSD_TO_LIFT
        else:
            return self.config.TRAVEL_TIME_DC_TO_LIFT
        
    @property
    def travel_time_dock_to_storage(self):
        """Returns the travel time from the unloading port to the storage area"""
        if self.unloading_port == Port.DSD:
            if self.cargo_type == CargoType.AMBIENT:
                return self.config.TRAVEL_TIME_DSD_TO_AMBIENT_STORAGE
            else:
                return self.config.TRAVEL_TIME_DSD_TO_FRESH_STORAGE
        else:
            if self.cargo_type == CargoType.AMBIENT:
                return self.config.TRAVEL_TIME_DC_TO_AMBIENT_STORAGE
            else:
                return self.config.TRAVEL_TIME_DC_TO_FRESH_STORAGE
    
    @property
    def travel_time_lift_to_storage(self):
        """Returns the travel time from the lift area to the storage area"""
        if self.cargo_type == CargoType.AMBIENT:
            return self.config.LIFT_FORKLIFT_TRANSPORT_DURATION_BUFFER_AMBIENT
        else:
            return self.config.LIFT_FORKLIFT_TRANSPORT_DURATION_BUFFER_FRESH
        
    @property
    def storage_unload_duration(self):
        """Returns the unload duration for the pallet at the storage area"""
        if self.cargo_type == CargoType.AMBIENT:
            return self.config.STORAGE_AREA_AMBIENT_FORKLIFT_UNLOAD_DURATION
        else:
            return self.config.STORAGE_AREA_FRESH_FORKLIFT_UNLOAD_DURATION
        
    @property
    def storage_load_duration(self):
        """Returns the load duration for the pallet at the storage area"""
        if self.cargo_type == CargoType.AMBIENT:
            return self.config.STORAGE_AREA_AMBIENT_FORKLIFT_LOAD_DURATION
        else:
            return self.config.STORAGE_AREA_FRESH_FORKLIFT_LOAD_DURATION
        
    @property
    def dock_load_duration(self):
        """Returns the load duration for the pallet at the dock area"""
        if self.unloading_port == Port.DSD:
            return self.config.DSD_FORKLIFT_LOAD_DURATION
        else:
            return self.config.DC_FORKLIFT_LOAD_DURATION
            
    @property
    def is_lift_required(self):
        """Returns True if the pallet requires a lift to be stored"""
        if self.cargo_type == CargoType.AMBIENT:
            return self.config.IS_AMBIENT_LIFT_REQUIRED
        else:
            return self.config.IS_FRESH_LIFT_REQUIRED
        
    def assign_inbound_planned_times(self):
        """Assigns planned times for the pallet"""
        self.inbound_unload_start_time_planed = self.arrival_time
        self.inbound_unload_finish_time_planed = self.inbound_unload_start_time_planed + self.dock_unload_duration
        self.inbound_register_time_finish_planned = (self.inbound_unload_finish_time_actual + self.check_pallet_duration) if self.inbound_unload_finish_time_actual else None
        self.inbound_left_loading_area_time_planned = self.inbound_register_time_finish_actual if self.inbound_register_time_finish_actual else None

        if self.is_lift_required:
            self.inbound_lift_area_arrival_time_planned = (self.inbound_left_loading_area_time_actual + self.travel_time_dock_to_lift) if self.inbound_left_loading_area_time_actual else None
            self.inbound_lift_loading_finish_time_planned = (self.inbound_lift_area_arrival_time_actual + self.config.LIFT_FORKLIFT_LOAD_DURATION) if self.inbound_lift_area_arrival_time_actual else None
            self.inbound_lift_unloading_finish_time_planned = (self.inbound_lift_loading_finish_time_actual + self.config.SINGLE_TRIP_LIFT_DURATION + self.config.LIFT_FORKLIFT_UNLOAD_DURATION) if self.inbound_lift_loading_finish_time_actual else None
            self.inbound_storage_area_arrival_time_planned = (self.inbound_lift_unloading_finish_time_actual + self.travel_time_lift_to_storage) if self.inbound_lift_unloading_finish_time_actual else None
        else:
            self.inbound_storage_area_arrival_time_planned = (self.inbound_left_loading_area_time_actual + self.travel_time_dock_to_storage) if self.inbound_left_loading_area_time_actual else None

    def assign_outbound_planned_times(self):
        """Assigns planned times for the outbound pallet"""
        self.outbound_storage_load_time_planned = self.arrival_time
        self.outbound_storage_load_finish_time_planned = (self.outbound_storage_load_time_actual+ self.storage_load_duration) if self.outbound_storage_load_time_actual else None
        self.outbound_left_storage_area_time_planned = self.outbound_storage_load_finish_time_actual

        if self.is_lift_required:
            self.outbound_lift_area_arrival_time_planned = (self.outbound_left_storage_area_time_actual + self.travel_time_lift_to_storage) if self.outbound_left_storage_area_time_actual else None
            self.outbound_lift_loading_finish_time_planned = (self.outbound_lift_area_arrival_time_actual + self.config.LIFT_FORKLIFT_LOAD_DURATION) if self.outbound_lift_area_arrival_time_actual else None
            self.outbound_lift_unloading_finish_time_planned = (self.outbound_lift_loading_finish_time_actual + self.config.SINGLE_TRIP_LIFT_DURATION + self.config.LIFT_FORKLIFT_UNLOAD_DURATION) if self.outbound_lift_loading_finish_time_actual else None
            self.outbound_lift_area_departure_time_planned = self.outbound_lift_unloading_finish_time_planned
            self.outbound_loading_area_arrival_time_planned = (self.outbound_lift_area_departure_time_actual + self.travel_time_dock_to_lift) if self.outbound_lift_area_departure_time_actual else None
        else:
            self.outbound_loading_area_arrival_time_planned = (self.outbound_left_storage_area_time_actual + self.travel_time_dock_to_storage) if self.outbound_left_storage_area_time_actual else None

        self.outbound_truck_load_time_planned = (self.outbound_loading_area_arrival_time_actual + self.dock_load_duration) if self.outbound_loading_area_arrival_time_actual else None

class Forklift(Entity):
    """Represents a forklift in the warehouse."""
    
    def __init__(self, env: simpy.Environment, config: ConfigParser, entity_id: int):
        super().__init__(env, config, entity_id)
        self.operation: str = "inbound"
        self.location: Optional[EntityLocation] = None
        self.pallet: Optional[Pallet] = None

    def load_pallet(self, pallet: Pallet):
        """Loads a pallet to the forklift"""
        if self.pallet:
            raise Exception("Forklift already has a pallet")
        
        # load the pallet if there is no pallet on the forklift
        self.pallet = pallet

    def unload_pallet(self):
        """Unloads a pallet from the forklift"""
        if not self.pallet:
            raise Exception("Forklift does not have a pallet")

        # unload the pallet if there is pallet on the forklift
        pallet = self.pallet
        self.pallet = None
        return pallet
    
    @property
    def is_available(self):
        """Returns True if the forklift is available"""
        return self.pallet is None
