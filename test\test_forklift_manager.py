"""
Test module for forklift manager.

created on 26.05.2025 by <PERSON><PERSON><PERSON>
"""

import simpy
from simpy.resources import store
import os
import sys
import unittest
import logging

logging.basicConfig(level=logging.DEBUG)

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.utils.forklift_manager import ForkliftManager
from src.utils.config_parser import ConfigParser
from src.models.entities import Pallet, Forklift
from src.models.model_enums import CargoType, EntityLocation, Port

class TestForkliftManager(unittest.TestCase):
    """Test class for forklift manager."""

    def _callTestMethod(self, method):
        import inspect

        if inspect.isgeneratorfunction(method):
            list(method())
        else:
            method()

    def setUp(self):
        """Set up the test."""
        self.env = simpy.Environment()
        self.config = ConfigParser()
        self.config.pass_yaml("src/config/config.yaml")
        self.forklift_manager = ForkliftManager(self.env, self.config)

    def test_inbound_forklifts(self):
        """Test forklift manager."""
        self.assertEqual(len(self.forklift_manager.passageway_forklift_pool.items), self.config.PASSAGEWAY_FORKLIFT_TRHROUGHPUT_LIMIT)
        self.assertEqual(len(self.forklift_manager.lift_forklift_pool.items), self.config.LIFT_AREA_FORKLIFT_LIMIT)
        self.assertEqual(len(self.forklift_manager.fresh_storage_forklift_pool.items), self.config.STORAGE_AREA_FRESH_FORKLIFT_LIMIT)
        self.assertEqual(len(self.forklift_manager.ambient_storage_forklift_pool.items), self.config.STORAGE_AREA_AMBIENT_FORKLIFT_LIMIT)
        self.assertEqual(len(self.forklift_manager.dc_forklift_pool.items), self.config.DC_FORKLIFT_OPERATION_LIMIT)
    
    def test_outbound_forklifts(self):
        """Test forklift manager."""
        passageway_outbound_forklift_count = sum(1 for forklift in self.forklift_manager.passageway_forklift_pool.items if forklift.operation == "both")
        self.assertEqual(passageway_outbound_forklift_count, self.config.PASSAGEWAY_FORKLIFT_TRHOUGHPUT_LIMIT_OUTBOUND)

        lift_outbound_forklift_count = sum(1 for forklift in self.forklift_manager.lift_forklift_pool.items if forklift.operation == "both")
        self.assertEqual(lift_outbound_forklift_count, self.config.LIFT_AREA_FORKLIFT_LIMIT_OUTBOUND)

        fresh_storage_outbound_forklift_count = sum(1 for forklift in self.forklift_manager.fresh_storage_forklift_pool.items if forklift.operation == "both")
        self.assertEqual(fresh_storage_outbound_forklift_count, self.config.STORAGE_AREA_FRESH_FORKLIFT_LIMIT_OUTBOUND)

        ambient_storage_outbound_forklift_count = sum(1 for forklift in self.forklift_manager.ambient_storage_forklift_pool.items if forklift.operation == "both")
        self.assertEqual(ambient_storage_outbound_forklift_count, self.config.STORAGE_AREA_AMBIENT_FORKLIFT_LIMIT_OUTBOUND)

        dc_outbound_forklift_count = sum(1 for forklift in self.forklift_manager.dc_forklift_pool.items if forklift.operation == "both")
        self.assertEqual(dc_outbound_forklift_count, self.config.DC_FORKLIFT_OUTBOUND_OPERATION_LIMIT)

        dsd_outbound_forklift_count = sum(1 for forklift in self.forklift_manager.dsd_forklift_pool.items if forklift.operation == "both")
        self.assertEqual(dsd_outbound_forklift_count, self.config.DSD_FORKLIFT_OUTBOUND_OPERATION_LIMIT)
    
    def test_get_dock_forklift(self):
        """Test get dock forklift."""
        pallet = Pallet(env=self.env, config=self.config, entity_id=0, store_id=0, unloading_port=Port.DSD, cargo_type=CargoType.AMBIENT, location=EntityLocation.STORAGE_AREA, truck_id=0, arrival_time=0)
        forklift = self.forklift_manager.request_dock_forklift(pallet, True)
        self.assertEqual(isinstance(forklift, store.FilterStoreGet), True)
       
if __name__ == "__main__":
    unittest.main(verbosity=2)