"""
Manages forklifts in the warehouse simulation.

created on 26.05.2025 by <PERSON><PERSON><PERSON>
"""

import simpy

from src.models.entities import Forklift, Pallet
from src.models.model_enums import Port, CargoType

class ForkliftManager:
    """Manages forklifts in the warehouse simulation."""

    def __init__(self, env, config):
        self.env = env
        self.config = config

        # create forklifts
        self.forklift_count = self.config.PASSAGEWAY_FORKLIFT_TRHROUGHPUT_LIMIT + \
            self.config.LIFT_AREA_FORKLIFT_LIMIT + \
            self.config.STORAGE_AREA_FRESH_FORKLIFT_LIMIT + \
            self.config.STORAGE_AREA_AMBIENT_FORKLIFT_LIMIT + \
            self.config.DC_FORKLIFT_OPERATION_LIMIT + \
            self.config.DSD_FORKLIFT_OPERATION_LIMIT
        
        self.forklifts = [Forklift(env, config, i) for i in range(self.forklift_count)]

        # assign forklifts to inbound operation
        self.passageway_forklift_pool = simpy.FilterStore(env)
        self.lift_forklift_pool = simpy.FilterStore(env)
        self.fresh_storage_forklift_pool = simpy.FilterStore(env)
        self.ambient_storage_forklift_pool = simpy.FilterStore(env)
        self.dc_forklift_pool = simpy.FilterStore(env)
        self.dsd_forklift_pool = simpy.FilterStore(env)

        for forklift in self.forklifts:
            if len(self.passageway_forklift_pool.items) < self.config.PASSAGEWAY_FORKLIFT_TRHROUGHPUT_LIMIT:
                self.passageway_forklift_pool.put(forklift)
            elif len(self.lift_forklift_pool.items) < self.config.LIFT_AREA_FORKLIFT_LIMIT:
                self.lift_forklift_pool.put(forklift)
            elif len(self.fresh_storage_forklift_pool.items) < self.config.STORAGE_AREA_FRESH_FORKLIFT_LIMIT:
                self.fresh_storage_forklift_pool.put(forklift)
            elif len(self.ambient_storage_forklift_pool.items) < self.config.STORAGE_AREA_AMBIENT_FORKLIFT_LIMIT:
                self.ambient_storage_forklift_pool.put(forklift)
            elif len(self.dc_forklift_pool.items) < self.config.DC_FORKLIFT_OPERATION_LIMIT:
                self.dc_forklift_pool.put(forklift)
            else:
                self.dsd_forklift_pool.put(forklift)
        
        # assign forklifts to outbound operation
        for _, forklift in zip(range(self.config.PASSAGEWAY_FORKLIFT_TRHOUGHPUT_LIMIT_OUTBOUND), self.passageway_forklift_pool.items):
                forklift.operation = "both"
        for _, forklift in zip(range(self.config.LIFT_AREA_FORKLIFT_LIMIT_OUTBOUND), self.lift_forklift_pool.items):
                forklift.operation = "both"
        for _, forklift in zip(range(self.config.STORAGE_AREA_FRESH_FORKLIFT_LIMIT_OUTBOUND), self.fresh_storage_forklift_pool.items):
                forklift.operation = "both"
        for _, forklift in zip(range(self.config.STORAGE_AREA_AMBIENT_FORKLIFT_LIMIT_OUTBOUND), self.ambient_storage_forklift_pool.items):
                forklift.operation = "both"
        for _, forklift in zip(range(self.config.DC_FORKLIFT_OUTBOUND_OPERATION_LIMIT), self.dc_forklift_pool.items):
                forklift.operation = "both"
        for _, forklift in zip(range(self.config.DSD_FORKLIFT_OUTBOUND_OPERATION_LIMIT), self.dsd_forklift_pool.items):
                forklift.operation = "both"

    def request_dock_forklift(self, pallet: Pallet, is_inbound: bool):
        """Get a forklift from the appropriate dock."""
        if is_inbound:
            if pallet.unloading_port == Port.DSD:
                return self.dsd_forklift_pool.get(lambda forklift: forklift.operation == "inbound" or forklift.operation == "both")
            else:
                 return self.dc_forklift_pool.get(lambda forklift: forklift.operation == "inbound" or forklift.operation == "both")
        else:
            if pallet.unloading_port == Port.DSD:
                return self.dsd_forklift_pool.get(lambda forklift: forklift.operation == "both")
            else:
                 return self.dc_forklift_pool.get(lambda forklift: forklift.operation == "both")
    
    def request_passageway_forklift(self, is_inbound: bool):
        """Get a forklift from the passageway."""
        if is_inbound:
            return self.passageway_forklift_pool.get(lambda forklift: forklift.operation == "inbound" or forklift.operation == "both")
        else:
            return self.passageway_forklift_pool.get(lambda forklift: forklift.operation == "both")

    def request_lift_forklift(self, is_inbound: bool):
        """Get a forklift from the lift area."""
        if is_inbound:
            return self.lift_forklift_pool.get(lambda forklift: forklift.operation == "inbound" or forklift.operation == "both")
        else:
            return self.lift_forklift_pool.get(lambda forklift: forklift.operation == "both")
    
    def request_storage_forklift(self, pallet: Pallet, is_inbound: bool):
        """Get a forklift from the fresh storage area."""
        if is_inbound:
            if pallet.cargo_type == CargoType.FRESH:
                return self.fresh_storage_forklift_pool.get(lambda forklift: forklift.operation == "inbound" or forklift.operation == "both")
            else:
                return self.ambient_storage_forklift_pool.get(lambda forklift: forklift.operation == "inbound" or forklift.operation == "both")
        else:
            if pallet.cargo_type == CargoType.FRESH:
                return self.fresh_storage_forklift_pool.get(lambda forklift: forklift.operation == "both")
            else:
                return self.ambient_storage_forklift_pool.get(lambda forklift: forklift.operation == "both")
            
    def release_dock_forklift(self, forklift: Forklift):
        """Release a forklift to the appropriate dock."""
        if forklift.pallet:
             if forklift.pallet.unloading_port == Port.DSD:
                self.dsd_forklift_pool.put(forklift)
             else:
                self.dc_forklift_pool.put(forklift)
        else:
             raise ValueError("Forklift does not have a pallet")
    
    def release_passageway_forklift(self, forklift: Forklift):
        """Release a forklift to the passageway."""
        if forklift.pallet:
            self.passageway_forklift_pool.put(forklift)
        else:
            raise ValueError("Forklift does not have a pallet")
    
    def release_lift_forklift(self, forklift: Forklift):
        """Release a forklift to the lift area."""
        if forklift.pallet:
            self.lift_forklift_pool.put(forklift)
        else:
            raise ValueError("Forklift does not have a pallet")
    
    def release_storage_forklift(self, forklift: Forklift):
        """Release a forklift to the storage area."""
        if forklift.pallet:
            if forklift.pallet.cargo_type == CargoType.FRESH:
                self.fresh_storage_forklift_pool.put(forklift)
            else:
                self.ambient_storage_forklift_pool.put(forklift)
        else:
            raise ValueError("Forklift does not have a pallet")  




