"""
Data loading module for warehouse unloading simulation.
Handles loading and parsing data from the input csv file.

created on 12.04.2025 by <PERSON><PERSON><PERSON>
"""

import os
import sys
import pandas as pd
from typing import Dict, List, Any

path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(path)

from src.models.model_enums import CargoType, EntityLocation, Port
from src.utils.config_parser import ConfigParser

class InputParser:
    """
    Class to parse and validate input data for the warehouse simulation.
    
    """

    def __init__(self, config: object, inbound_path: str, outbound_path: str):
        """
        Initialize the input parser.
        """
        self.config = config
        self.inbound_path = inbound_path
        self.outbound_path = outbound_path
        self.inbound_data = []
        self.outbound_data = []
        
        # process the inbound data
        if not os.path.exists(self.inbound_path):
            raise FileNotFoundError(f"Input file not found: {self.inbound_path}")
        elif not self.inbound_path.endswith('.csv'):
            raise ValueError(f"Unsupported inbound data file format: {self.inbound_path}")
        else:
            inbound_df = pd.read_csv(self.inbound_path)
            self.inbound_data = self._process_inbound_data(inbound_df)

        # process the outbound data
        if not os.path.exists(self.outbound_path):
            raise FileNotFoundError(f"Input file not found: {self.outbound_path}")
        elif not self.outbound_path.endswith('.csv'):
            raise ValueError(f"Unsupported outbound data file format: {self.outbound_path}")
        else:
            outbound_df = pd.read_csv(self.outbound_path)
            self.outbound_data = self._process_outbound_data(outbound_df)


    def _process_inbound_data(self, inbound_df: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        Process inbound data and arrange by truck.
        """

        # Clean column names
        inbound_df.columns = [col.strip().lower() for col in inbound_df.columns]

        # Handle time values
        # Convert the arrival_time column to datetime.time objects
        inbound_df.loc[:, 'arrival_time'] = pd.to_datetime(inbound_df.loc[:, 'arrival_time'], format='%H:%M:%S')

        # Convert datetime.time to seconds since midnight
        inbound_df.loc[:, 'arrival_time'] = inbound_df.loc[:, 'arrival_time'].apply(lambda x: x.hour * 3600 + x.minute * 60 + x.second)

        # change the cargo type to ambient or fresh
        inbound_df.loc[:, 'cargo_type'] = inbound_df.loc[:,'cargo_type'].apply(lambda x: self._get_cargo_type(x).value)

        # Rearrange each group by arrival time
        inbound_df = inbound_df.sort_values(by=['arrival_time', 'cargo_type'])
        inbound_df = inbound_df.reset_index(drop=True)

        # Initialize trucks collection
        trucks = []

        # Group pallets by vehicles
        trucks_groups = inbound_df.groupby('vehicle_id')

        for truck_id, group in trucks_groups:
            arrival_time = group.loc[:, "arrival_time"].min()
            truck_entry = {
                "entity_id": str(truck_id),
                "store_id": int(group["store_id"].unique()[0]),
                "unloading_port": self._get_unloading_port(group["unloading_port"].unique()[0]),
                "arrival_time": int(arrival_time),
                "pallets": []
            }
            for _, row in group.iterrows():
                pallet = {
                    "entity_id": str(row["pallet_id"]),
                    "store_id": int(row["store_id"]),
                    "unloading_port": self._get_unloading_port(row["unloading_port"]),
                    "cargo_type": self._get_cargo_type(row["cargo_type"]),
                    "location": EntityLocation.TRUCK,
                    "truck_id": int(str(truck_id)),
                    "arrival_time": int(arrival_time)
                }
                truck_entry["pallets"].append(pallet)
            trucks.append(truck_entry)

        # Sort vehicles by arrival time
        trucks.sort(key=lambda x: x["arrival_time"])

        return trucks
    
    def _process_outbound_data(self, outbound_df: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        Process outbound data and arrange by truck.
        """
        # Clean column names
        outbound_df.columns = [col.strip().lower() for col in outbound_df.columns]

        # Handle time values
        # Convert the arrival_time column to datetime.time objects
        outbound_df.loc[:, 'outbound_ready_time'] = pd.to_datetime(outbound_df.loc[:, 'outbound_ready_time'], format='%H:%M:%S')

        # Convert datetime.time to seconds since midnight
        outbound_df.loc[:, 'outbound_ready_time'] = outbound_df.loc[:, 'outbound_ready_time'].apply(lambda x: x.hour * 3600 + x.minute * 60 + x.second)

        # change the cargo type to ambient or fresh
        outbound_df.loc[:, 'cargo_type'] = outbound_df.loc[:, 'cargo_type'].apply(lambda x: self._get_cargo_type(x).value)

        # Rearrange each group by arrival time
        outbound_df = outbound_df.sort_values(by=['outbound_ready_time', 'cargo_type'])
        outbound_df = outbound_df.reset_index(drop=True)

        # Initialize pallet collection
        pallets = []

        for _, row in outbound_df.iterrows():
            pallet = {
                "entity_id": str(row["pallet_id"]),
                "store_id": int(row["store_id"]),
                "unloading_port": self._get_unloading_port(row["unloading_port"]),
                "cargo_type": self._get_cargo_type(row["cargo_type"]),
                "location": EntityLocation.STORAGE_AREA,
                "truck_id": None,
                "arrival_time": int(row["outbound_ready_time"])
            }
            pallets.append(pallet)

        return pallets

    def _get_unloading_port(self, port: str) -> Port:
        """
        Get the unloading port from the input data.
        """
        # process the input string
        port = port.strip().upper()

        if port == "DC":
            return Port.DC
        elif port == "DSD":
            return Port.DSD
        else:
            raise ValueError(f"Invalid unloading port: {port}")
        
    def _get_cargo_type(self, cargo_type: str) -> CargoType:
        """
        Get the cargo type from the input data.
        """
        # process the input string
        cargo_type = cargo_type.strip().lower()

        if cargo_type == "fresh" or cargo_type == "pdc" or cargo_type == "dsd":
            return CargoType.FRESH
        elif cargo_type == "ambient" or cargo_type == "cn" or cargo_type == "adc":
            return CargoType.AMBIENT
        else:
            raise ValueError(f"Invalid cargo type: {cargo_type}")
        
if __name__ == "__main__":

    config = ConfigParser()
    config.pass_yaml("src/config/config.yaml")

    data_parser = InputParser(config, "src/data/input_data_inbound_6518_v1.csv", "src/data/input_data_outbound_6518_v1.csv")
    print("\nInbound Data Sample:")
    print(data_parser.inbound_data[:1])
    print("\nOutbound Data Sample:")
    print(data_parser.outbound_data[:1])