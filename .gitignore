# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/
.env
.venv
env.bak/
venv.bak/

# IDE - VSCode
.vscode/
*.code-workspace
.history/

# IDE - Jupyter Notebook
.ipynb_checkpoints

# Testing
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Project specific
*.log
logs/
data/*.json
!tests/test_data/*.json
output/
results/
data/

# Operating System
.DS_Store
Thumbs.db
*.swp
*~

# Misc
*.bak
*.tmp
*.temp