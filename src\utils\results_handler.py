"""
Results handling module for warehouse unloading simulation.
Handles inbound and outbound simulation results.

created on 14.04.2025 by <PERSON><PERSON><PERSON>
updated on 16.07.2025
"""

from typing import List
import pandas as pd
import logging

# setup logging
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
formatter = logging.Formatter('%(levelname)s - %(name)s - %(message)s')
filehandler = logging.FileHandler('./src/logs/simulation.log')
filehandler.setFormatter(formatter)
logger.addHandler(filehandler)

from src.models.model_enums import EntityLocation, CargoType, Port
from src.models.entities import <PERSON><PERSON><PERSON>

def handle_inbound_results(pallets: List['Pallet'], result_save_path: str) -> None:
    """
    Handle inbound simulation results.
    """
    # store the inbound simulation results
    result_columns = ["store_id", "pallet_id", "unloading_port", "cargo_type", "vehicle_id", "arrival_time", "pallet_unload_start_time_planned", 
                        "pallet_unload_start_time_actual", "pallet_unload_finish_time_planned", "register_time_finish_planned", "register_time_finish_actual",
                        "pallet_left_loading_area_time_planned", "pallet_left_loading_area_time_actual", "lift_area_arrival_time_planned", "lift_area_arrival_time_actual",
                        "lift_loading_finish_time_planned", "lift_loading_finish_time_actual", "lift_unloading_finish_time_planned", "storage_area_arrival_time_planned",
                        "storage_area_arrival_time_actual", "pallet_stored_time"]
    result_df = pd.DataFrame(columns=result_columns)

    # process the simulation results
    for pallet in pallets:
        # assign planned times
        pallet.assign_inbound_planned_times()

        # add the pallet data to the result dataframe
        data_map = {"store_id": pallet.store_id, 
                                   "pallet_id": pallet.entity_id, 
                                   "unloading_port": pallet.unloading_port.value, 
                                   "cargo_type": pallet.cargo_type.value, 
                                   "vehicle_id": pallet.truck_id, 
                                   "arrival_time": pallet.arrival_time, 
                                   "pallet_unload_start_time_planned": pallet.inbound_unload_start_time_planed, 
                                   "pallet_unload_start_time_actual": pallet.inbound_unload_start_time_actual, 
                                   "pallet_unload_finish_time_planned": pallet.inbound_unload_finish_time_planed, 
                                   "register_time_finish_planned": pallet.inbound_register_time_finish_planned, 
                                   "register_time_finish_actual": pallet.inbound_register_time_finish_actual,
                                   "pallet_left_loading_area_time_planned": pallet.inbound_left_loading_area_time_planned, 
                                   "pallet_left_loading_area_time_actual": pallet.inbound_left_loading_area_time_actual, 
                                   "lift_area_arrival_time_planned": pallet.inbound_lift_area_arrival_time_planned, 
                                   "lift_area_arrival_time_actual": pallet.inbound_lift_area_arrival_time_actual,
                                   "lift_loading_finish_time_planned": pallet.inbound_lift_loading_finish_time_planned, 
                                   "lift_loading_finish_time_actual": pallet.inbound_lift_loading_finish_time_actual, 
                                   "lift_unloading_finish_time_planned": pallet.inbound_lift_unloading_finish_time_planned, 
                                   "storage_area_arrival_time_planned": pallet.inbound_storage_area_arrival_time_planned,
                                   "storage_area_arrival_time_actual": pallet.inbound_storage_area_arrival_time_actual, 
                                   "pallet_stored_time": pallet.inbound_pallet_stored_time
                                   }
        
        # append the pallet data to the result dataframe
        result_df = pd.concat([result_df, pd.DataFrame([data_map])], ignore_index=True)

    # store the inbound simulation results
    result_df.to_csv(result_save_path, index=False)

def handle_outbound_results(pallets: List['Pallet'], result_save_path: str) -> None:
    """
    Handle outbound simulation results.
    """
     # store the outbound simulation results
    result_columns = ["store_id", "inbound", "pallet_id", "unloading_port", "cargo_type", "vehicle_id", "outbound_ready_time", 
                        "pallet_storage_load_time_planned", "pallet_storage_load_time_actual", "pallet_storage_load_finish_time_planned",
                        "pallet_storage_load_finish_time_actual", "pallet_left_storage_area_time_planned", "pallet_left_storage_area_time_actual", "lift_area_arrival_time_planned", 
                        "lift_area_arrival_time_actual", "lift_loading_finish_time_planned", "lift_loading_finish_time_actual", "lift_unloading_finish_time_planned", 
                        "lift_unloading_finish_time_actual", "lift_area_depart_time_planned", "lift_area_depart_time_actual", "loading_area_arrival_time_planned", 
                        "loading_area_arrival_time_actual", "pallet_loaded_vehicle_time_planned", "pallet_loaded_vehicle_time_actual"
                        ]
    result_df = pd.DataFrame(columns=result_columns)

    # process the simulation results
    for pallet in pallets:
        # assign planned times
        pallet.assign_outbound_planned_times()

        # add the pallet data to the result dataframe
        data_map = {"store_id": pallet.store_id,
                    "inbound": "N", 
                    "pallet_id": pallet.entity_id, 
                    "unloading_port": pallet.unloading_port.value, 
                    "cargo_type": pallet.cargo_type.value, 
                    "vehicle_id": pallet.truck_id, 
                    "outbound_ready_time": pallet.arrival_time, 
                    "pallet_storage_load_time_planned": pallet.outbound_storage_load_time_planned,
                    "pallet_storage_load_time_actual": pallet.outbound_storage_load_time_actual,
                    "pallet_storage_load_finish_time_planned": pallet.outbound_storage_load_finish_time_planned,
                    "pallet_storage_load_finish_time_actual": pallet.outbound_storage_load_finish_time_actual,
                    "pallet_left_storage_area_time_planned": pallet.outbound_left_storage_area_time_planned,
                    "pallet_left_storage_area_time_actual": pallet.outbound_left_storage_area_time_actual,
                    "lift_area_arrival_time_planned": pallet.outbound_lift_area_arrival_time_planned,
                    "lift_area_arrival_time_actual": pallet.outbound_lift_area_arrival_time_actual,
                    "lift_loading_finish_time_planned" : pallet.outbound_lift_loading_finish_time_planned,
                    "lift_loading_finish_time_actual": pallet.outbound_lift_loading_finish_time_actual,
                    "lift_unloading_finish_time_planned": pallet.outbound_lift_unloading_finish_time_planned,
                    "lift_unloading_finish_time_actual": pallet.outbound_lift_unloading_finish_time_actual,
                    "lift_area_depart_time_planned": pallet.outbound_lift_area_departure_time_planned,
                    "lift_area_depart_time_actual": pallet.outbound_lift_area_departure_time_actual,
                    "loading_area_arrival_time_planned": pallet.outbound_loading_area_arrival_time_planned,
                    "loading_area_arrival_time_actual": pallet.outbound_loading_area_arrival_time_actual,
                    "pallet_loaded_vehicle_time_planned": pallet.outbound_truck_load_time_planned,
                    "pallet_loaded_vehicle_time_actual": pallet.outbound_truck_load_time_actual
                    }
        
        # append the pallet data to the result dataframe
        result_df = pd.concat([result_df, pd.DataFrame([data_map])], ignore_index=True)

    # store the inbound simulation results
    result_df.to_csv(result_save_path, index=False)

def track_inbound_pallet_location(pallets: List['Pallet']) -> None:
    """
    Track the location of a pallet.
    """
    print("\nINBOUND PALLET LOCATIONS")
    print("=========================")

    location_track ={
        "truck": 0,
        "unloading_area": 0,
        "passageway": 0,
        "lift_area": 0,
        "lift": 0,
        "storage_area": 0
    }

    # track pallets in truck
    truck_pallets = []
    unloading_area_pallets = []
    passageway_pallets = []
    lift_area_pallets = []
    lift_pallets = []
    storage_area_pallets = []

    for pallet in pallets:
        if pallet.location == EntityLocation.TRUCK:
            truck_pallets.append(pallet)
        elif pallet.location == EntityLocation.UNLOADING_AREA:
            unloading_area_pallets.append(pallet)
        elif pallet.location == EntityLocation.PASSAGEWAY:
            passageway_pallets.append(pallet)
        elif pallet.location == EntityLocation.LIFT_AREA:
            lift_area_pallets.append(pallet)
        elif pallet.location == EntityLocation.LIFT:
            lift_pallets.append(pallet)
        elif pallet.location == EntityLocation.STORAGE_AREA:
            storage_area_pallets.append(pallet)

    print(f"No of pallets in truck: {len(truck_pallets)}")
    print(f"No of pallets in unloading area: {len(unloading_area_pallets)}")
    print(f"No of pallets in passageway: {len(passageway_pallets)}")
    print(f"No of pallets in lift area: {len(lift_area_pallets)}")
    print(f"No of pallets in lift: {len(lift_pallets)}")
    print(f"No of pallets in storage area: {len(storage_area_pallets)}")

    logger.debug(f"inbound truck pallets: {[pallet.entity_id for pallet in truck_pallets]}")
    logger.debug(f"Inbound unloading area pallets: {[pallet.entity_id for pallet in unloading_area_pallets]}")
    logger.debug(f"Inbound passageway pallets: {[pallet.entity_id for pallet in passageway_pallets]}")
    logger.debug(f"Inbound lift area pallets: {[pallet.entity_id for pallet in lift_area_pallets]}")
    logger.debug(f"Inbound lift pallets: {[pallet.entity_id for pallet in lift_pallets]}")
    logger.debug(f"inbound storage area pallets: {[pallet.entity_id for pallet in storage_area_pallets]}")

def track_outbound_pallet_location(pallets: List['Pallet']) -> None:
    """
    Track the location of a pallet.
    """
    print("\nOUTBOUND PALLET LOCATIONS")
    print("=========================")

    location_track ={
        "truck": 0,
        "unloading_area": 0,
        "passageway": 0,
        "lift_area": 0,
        "lift": 0,
        "storage_area": 0
    }

    # track pallets in storage area
    storage_area_pallets = []
    lift_area_pallets = []
    lift_pallets = []
    unloading_area_pallets = []
    passageway_pallets = []
    truck_pallets = []

    for pallet in pallets:
        if pallet.location == EntityLocation.TRUCK:
            truck_pallets.append(pallet)
        elif pallet.location == EntityLocation.UNLOADING_AREA:
            unloading_area_pallets.append(pallet)
        elif pallet.location == EntityLocation.PASSAGEWAY:
            passageway_pallets.append(pallet)
        elif pallet.location == EntityLocation.LIFT_AREA:
            lift_area_pallets.append(pallet)
        elif pallet.location == EntityLocation.LIFT:
            lift_pallets.append(pallet)
        elif pallet.location == EntityLocation.STORAGE_AREA:
            storage_area_pallets.append(pallet)
            
    print(f"No of pallets in truck: {len(truck_pallets)}")
    print(f"No of pallets in unloading area: {len(unloading_area_pallets)}")
    print(f"No of pallets in passageway: {len(passageway_pallets)}")
    print(f"No of pallets in lift area: {len(lift_area_pallets)}")
    print(f"No of pallets in lift: {len(lift_pallets)}")
    print(f"No of pallets in storage area: {len(storage_area_pallets)}")

    logger.debug(f"Outbound storage area pallets: {[pallet.entity_id for pallet in storage_area_pallets]}")
    logger.debug(f"Outnbound lift area pallets: {[pallet.entity_id for pallet in lift_area_pallets]}")
    logger.debug(f"Outbound lift pallets: {[pallet.entity_id for pallet in lift_pallets]}")
    logger.debug(f"outbound passageway pallets: {[pallet.entity_id for pallet in passageway_pallets]}")
    logger.debug(f"Outbound unloading area pallets: {[pallet.entity_id for pallet in unloading_area_pallets]}")
    logger.debug(f"Outbound truck pallets: {[pallet.entity_id for pallet in truck_pallets]}")
    
def handle_forklift_results(inbound_pallets: List['Pallet'], outbound_pallets: List['Pallet']) -> pd.DataFrame:
    """
    Handle forklift simulation results.
    """ 
    # dictionary to store forklift results
    forklift_results = {}

    def _get_forklift_time(on_time, off_time):
        """Returns the forklift time in seconds"""

        on_hour = on_time // 3600 if on_time else 0
        off_hour = off_time // 3600 if off_time else 0

        # divide time between on and off hour
        if on_hour != off_hour:
            # calculate the time on the forklift at on hour
            time_on_hour = off_hour*3600 - on_time
            # calculate the time on the forklift at off hour
            time_off_hour = off_time - off_hour*3600
            on_hour_str = f"{str(on_hour).zfill(2)}:00" if on_hour < 24 else f"Next Day {str(on_hour - 24)}:00"
            off_hour_str = f"{str(off_hour).zfill(2)}:00" if off_hour < 24 else f"Next Day {str(off_hour - 24)}:00"

        else:
            time_on_hour = off_time - on_time
            time_off_hour = 0
            on_hour_str = f"{str(on_hour).zfill(2)}:00" if on_hour < 24 else f"Next Day {str(on_hour - 24)}:00"
            off_hour_str = f"{str(off_hour + 1).zfill(2)}:00" if off_hour < 23 else f"Next Day {str(off_hour + 1 - 24)}:00"

        return {on_hour_str: time_on_hour, off_hour_str: time_off_hour}
    
    def _put_forklift_time(forklift_results, forklift_area, forklift_time_dict):
        """Put the forklift time in the forklift results dictionary"""
        on_hour_str = list(forklift_time_dict.keys())[0]
        off_hour_str = list(forklift_time_dict.keys())[1]
        on_hour_time = forklift_time_dict[on_hour_str]
        off_hour_time = forklift_time_dict[off_hour_str]

        # put the forklift time in the forklift results dictionary
        if on_hour_str in forklift_results:
            if forklift_area in forklift_results[on_hour_str]:
                forklift_results[on_hour_str][forklift_area] += on_hour_time
            else:
                forklift_results[on_hour_str][forklift_area] = on_hour_time
        else:
            forklift_results[on_hour_str] = {forklift_area: on_hour_time}

        if off_hour_str in forklift_results:
            if forklift_area in forklift_results[off_hour_str]:
                forklift_results[off_hour_str][forklift_area] += off_hour_time
            else:
                forklift_results[off_hour_str][forklift_area] = off_hour_time
        else:
            forklift_results[off_hour_str] = {forklift_area: off_hour_time}

    # process inbound pallets
    for pallet in inbound_pallets + outbound_pallets:
        # calculate the dock forklift times
        if pallet.dock_forklift_on_time and pallet.dock_forklift_off_time:
            dock_forklift_dict = _get_forklift_time(pallet.dock_forklift_on_time, pallet.dock_forklift_off_time)
            
            if pallet.unloading_port == Port.DSD:
                _put_forklift_time(forklift_results, "dsd_forklift_utilization", dock_forklift_dict)
            else:
                _put_forklift_time(forklift_results, "dc_forklift_utilization", dock_forklift_dict)

        if pallet.passageway_forklift_on_time and pallet.passageway_forklift_off_time:
            passageway_forklift_dict = _get_forklift_time(pallet.passageway_forklift_on_time, pallet.passageway_forklift_off_time)
            _put_forklift_time(forklift_results, "passageway_forklift_utilization", passageway_forklift_dict)

        if pallet.lift_forklift_on_time and pallet.lift_forklift_off_time:
            lift_forklift_dict = _get_forklift_time(pallet.lift_forklift_on_time, pallet.lift_forklift_off_time)
            _put_forklift_time(forklift_results, "lift_forklift_utilization", lift_forklift_dict)

        if pallet.storage_forklift_on_time and pallet.storage_forklift_off_time:
            storage_forklift_dict = _get_forklift_time(pallet.storage_forklift_on_time, pallet.storage_forklift_off_time)

            if pallet.cargo_type == CargoType.AMBIENT:
                _put_forklift_time(forklift_results, "ambient_storage_forklift_utilization", storage_forklift_dict)
            else:
                _put_forklift_time(forklift_results, "fresh_storage_forklift_utilization", storage_forklift_dict)

    # convert the dict into dataframe
    df = pd.DataFrame.from_dict(forklift_results, orient='index')

    # rearrange by the index
    df = df.sort_index()
    df.index.name = "hour"

    # rearrange the columns
    # check the columns available in the df and arrange accordingly
    column_names = []
    for column in df.columns:
        if column in ["dsd_forklift_utilization", "dc_forklift_utilization", "passageway_forklift_utilization", "lift_forklift_utilization", "fresh_storage_forklift_utilization", "ambient_storage_forklift_utilization"]:
            column_names.append(column)
    df = df[column_names]

    # return the dataframe
    return df

def handle_lift_results(inbound_pallets: List['Pallet'], outbound_pallets: List['Pallet']) -> pd.DataFrame:
    """
    Handle lift simulation results.
    """ 
    # dictionary to store lift results
    lift_results = {}

    def _get_lift_time(on_time, off_time):
        """Returns the lift time in seconds"""

        on_hour = on_time // 3600 if on_time else 0
        off_hour = off_time // 3600 if off_time else 0

        # divide time between on and off hour
        if on_hour != off_hour:
            # calculate the time on the lift at on hour
            time_on_hour = off_hour*3600 - on_time
            # calculate the time on the lift at off hour
            time_off_hour = off_time - off_hour*3600
            on_hour_str = f"{str(on_hour).zfill(2)}:00" if on_hour < 24 else f"Next Day {str(on_hour - 24)}:00"
            off_hour_str = f"{str(off_hour).zfill(2)}:00" if off_hour < 24 else f"Next Day {str(off_hour - 24)}:00"

        else:
            time_on_hour = off_time - on_time
            time_off_hour = 0
            on_hour_str = f"{str(on_hour).zfill(2)}:00" if on_hour < 24 else f"Next Day {str(on_hour - 24)}:00"
            off_hour_str = f"{str(off_hour + 1).zfill(2)}:00" if off_hour < 23 else f"Next Day {str(off_hour + 1 - 24)}:00"

        return {on_hour_str: time_on_hour, off_hour_str: time_off_hour}
    
    def _put_lift_time(lift_results, lift_time_dict):
        """Put the ift time in the lift results dictionary"""
        on_hour_str = list(lift_time_dict.keys())[0]
        off_hour_str = list(lift_time_dict.keys())[1]
        on_hour_time = lift_time_dict[on_hour_str]
        off_hour_time = lift_time_dict[off_hour_str]

        # put the lift time in the lift results dictionary
        if on_hour_str in lift_results:
            lift_results[on_hour_str] += on_hour_time
        else:
            lift_results[on_hour_str] = on_hour_time

        if off_hour_str in lift_results:
            lift_results[off_hour_str] += off_hour_time
        else:
            lift_results[off_hour_str] = off_hour_time

    # process inbound pallets
    for pallet in inbound_pallets + outbound_pallets:
        # calculate the dock lift times
        if pallet.lift_on_time and pallet.lift_off_time:
            lift_dict = _get_lift_time(pallet.lift_on_time, pallet.lift_off_time)
            _put_lift_time(lift_results, lift_dict)

    # convert the dict into dataframe
    df = pd.DataFrame.from_dict(lift_results, orient='index')

    # rearrange by the index
    df = df.sort_index()
    df.index.name = "hour"

    # rename the column
    df.columns = ["lift_utilization"]

    # return the dataframe
    return df